#!/usr/bin/env python3
"""
Script to verify Firebase database storage for daily summary messages.
This script directly queries the Firebase database to check:
1. Whether summary messages are being written correctly
2. Message structure and field values
3. Chat session association
4. App ID and plugin ID matching
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from google.cloud import firestore
from google.cloud.firestore_v1.base_query import FieldFilter

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize Firebase
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = '/home/<USER>/omi/backend/google-credentials.json'
db = firestore.Client()

def verify_user_messages(uid: str):
    """Verify messages for a specific user"""
    print(f"\n{'='*80}")
    print(f"🔍 VERIFYING DATABASE STORAGE FOR USER: {uid[:8]}...")
    print(f"{'='*80}")
    
    user_ref = db.collection('users').document(uid)
    
    # 1. Check all messages for this user
    print(f"\n📄 CHECKING ALL MESSAGES:")
    print(f"-" * 50)
    
    messages_ref = user_ref.collection('messages')
    messages_query = messages_ref.order_by('created_at', direction=firestore.Query.DESCENDING).limit(20)
    
    all_messages = []
    for doc in messages_query.stream():
        message_data = doc.to_dict()
        all_messages.append(message_data)
        
        # Check if this is a recent daily summary
        created_at = message_data.get('created_at')
        message_type = message_data.get('type', 'unknown')
        app_id = message_data.get('app_id')
        plugin_id = message_data.get('plugin_id')
        chat_session_id = message_data.get('chat_session_id')
        sender = message_data.get('sender', 'unknown')
        text_preview = message_data.get('text', '')[:100] + '...' if len(message_data.get('text', '')) > 100 else message_data.get('text', '')
        
        # Check if message is from last 24 hours
        if created_at:
            time_diff = datetime.now(timezone.utc) - created_at
            is_recent = time_diff < timedelta(hours=24)
            time_str = created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        else:
            is_recent = False
            time_str = "No timestamp"
        
        print(f"   📝 Message ID: {doc.id}")
        print(f"      ⏰ Created: {time_str} {'🆕' if is_recent else ''}")
        print(f"      👤 Sender: {sender}")
        print(f"      🏷️  Type: {message_type}")
        print(f"      🔗 App ID: {app_id}")
        print(f"      🔗 Plugin ID: {plugin_id}")
        print(f"      💬 Chat Session: {chat_session_id}")
        print(f"      📄 Text Preview: {text_preview}")
        print(f"      ---")
    
    print(f"\n📊 TOTAL MESSAGES FOUND: {len(all_messages)}")
    
    # 2. Check chat sessions
    print(f"\n🗂️  CHECKING CHAT SESSIONS:")
    print(f"-" * 50)
    
    sessions_ref = user_ref.collection('chat_sessions')
    sessions_query = sessions_ref.order_by('created_at', direction=firestore.Query.DESCENDING)
    
    all_sessions = []
    for doc in sessions_query.stream():
        session_data = doc.to_dict()
        all_sessions.append(session_data)
        
        session_id = session_data.get('id', doc.id)
        app_id = session_data.get('app_id')
        plugin_id = session_data.get('plugin_id')
        message_ids = session_data.get('message_ids', [])
        created_at = session_data.get('created_at')
        title = session_data.get('title', 'No title')
        
        if created_at:
            time_str = created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        else:
            time_str = "No timestamp"
        
        print(f"   🗂️  Session ID: {session_id}")
        print(f"      ⏰ Created: {time_str}")
        print(f"      🏷️  Title: {title}")
        print(f"      🔗 App ID: {app_id}")
        print(f"      🔗 Plugin ID: {plugin_id}")
        print(f"      📝 Message Count: {len(message_ids)}")
        print(f"      📝 Message IDs: {message_ids[:3]}{'...' if len(message_ids) > 3 else ''}")
        print(f"      ---")
    
    print(f"\n📊 TOTAL CHAT SESSIONS FOUND: {len(all_sessions)}")
    
    # 3. Check for daily summary messages specifically
    print(f"\n📋 CHECKING DAILY SUMMARY MESSAGES:")
    print(f"-" * 50)
    
    summary_messages = [msg for msg in all_messages if msg.get('type') == 'day_summary']
    print(f"   📊 Total daily summary messages: {len(summary_messages)}")
    
    for i, msg in enumerate(summary_messages[:5]):  # Show last 5 summary messages
        created_at = msg.get('created_at')
        if created_at:
            time_diff = datetime.now(timezone.utc) - created_at
            is_recent = time_diff < timedelta(hours=24)
            time_str = created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        else:
            is_recent = False
            time_str = "No timestamp"
        
        print(f"   📋 Summary #{i+1}:")
        print(f"      ⏰ Created: {time_str} {'🆕' if is_recent else ''}")
        print(f"      🔗 App ID: {msg.get('app_id')}")
        print(f"      🔗 Plugin ID: {msg.get('plugin_id')}")
        print(f"      💬 Chat Session: {msg.get('chat_session_id')}")
        print(f"      📏 Text Length: {len(msg.get('text', ''))}")
        print(f"      📄 Text Preview: {msg.get('text', '')[:150]}...")
        print(f"      ---")
    
    # 4. Check what the API would return
    print(f"\n🔍 SIMULATING API QUERY (plugin_id=None):")
    print(f"-" * 50)
    
    # This simulates what the chat interface does
    messages_ref = user_ref.collection('messages')
    api_query = messages_ref.where(filter=FieldFilter('plugin_id', '==', None)).order_by('created_at', direction=firestore.Query.DESCENDING).limit(20)
    
    api_messages = []
    for doc in api_query.stream():
        message_data = doc.to_dict()
        api_messages.append(message_data)
    
    print(f"   📊 Messages returned by API query: {len(api_messages)}")
    
    for i, msg in enumerate(api_messages[:5]):  # Show first 5 messages
        created_at = msg.get('created_at')
        if created_at:
            time_str = created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        else:
            time_str = "No timestamp"
        
        print(f"   📝 API Message #{i+1}:")
        print(f"      ⏰ Created: {time_str}")
        print(f"      👤 Sender: {msg.get('sender')}")
        print(f"      🏷️  Type: {msg.get('type')}")
        print(f"      🔗 Plugin ID: {msg.get('plugin_id')}")
        print(f"      📄 Text Preview: {msg.get('text', '')[:100]}...")
        print(f"      ---")
    
    # 5. Check for session with plugin_id=None
    print(f"\n🗂️  CHECKING SESSION WITH plugin_id=None:")
    print(f"-" * 50)
    
    sessions_ref = user_ref.collection('chat_sessions')
    none_session_query = sessions_ref.where(filter=FieldFilter('plugin_id', '==', None)).limit(1)
    
    none_sessions = list(none_session_query.stream())
    if none_sessions:
        session_doc = none_sessions[0]
        session_data = session_doc.to_dict()
        print(f"   ✅ Found session with plugin_id=None:")
        print(f"      🗂️  Session ID: {session_data.get('id', session_doc.id)}")
        print(f"      📝 Message Count: {len(session_data.get('message_ids', []))}")
        print(f"      📝 Recent Message IDs: {session_data.get('message_ids', [])[-5:]}")
    else:
        print(f"   ❌ No session found with plugin_id=None")
    
    return {
        'total_messages': len(all_messages),
        'total_sessions': len(all_sessions),
        'summary_messages': len(summary_messages),
        'api_messages': len(api_messages),
        'has_none_session': len(none_sessions) > 0
    }

def main():
    """Main function to verify database storage"""
    print(f"🔍 FIREBASE DATABASE STORAGE VERIFICATION")
    print(f"⏰ Timestamp: {datetime.now(timezone.utc).isoformat()}")
    
    # Test with the user we know has recent activity
    test_uid = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    results = verify_user_messages(test_uid)
    
    print(f"\n{'='*80}")
    print(f"📊 VERIFICATION SUMMARY")
    print(f"{'='*80}")
    print(f"   📄 Total messages in database: {results['total_messages']}")
    print(f"   🗂️  Total chat sessions: {results['total_sessions']}")
    print(f"   📋 Daily summary messages: {results['summary_messages']}")
    print(f"   🔍 Messages returned by API: {results['api_messages']}")
    print(f"   🗂️  Has session with plugin_id=None: {results['has_none_session']}")
    
    # Analysis
    print(f"\n🔍 ANALYSIS:")
    if results['summary_messages'] > 0 and results['api_messages'] == 0:
        print(f"   ⚠️  ISSUE DETECTED: Summary messages exist but API returns none")
        print(f"   💡 This suggests a plugin_id/app_id mismatch issue")
    elif results['summary_messages'] > 0 and results['api_messages'] > 0:
        print(f"   ✅ Messages are being stored and retrieved correctly")
    elif results['summary_messages'] == 0:
        print(f"   ⚠️  No daily summary messages found in database")
    
    if not results['has_none_session']:
        print(f"   ⚠️  No chat session with plugin_id=None found")
        print(f"   💡 This could prevent messages from being associated correctly")

if __name__ == "__main__":
    main()
