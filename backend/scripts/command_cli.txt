

#运行Modal的推送脚本
python3 -m modal run modal/test_notifications_teague.py::test_daily_summary
python3 -m modal run modal/test_notifications_teague.py::test_daily_reminder
python3 -m modal run modal/test_notifications_teague.py::test_both_notifications

python3 -m modal run modal/job_modal_standalone.py::notifications_cronjob #每分钟检查时区并在适当时间发送
python3 -m modal run modal/job_modal_standalone.py::manual_notification_trigger #只在正确时区发送
python3 -m modal run modal/job_modal_standalone.py::manual_notification_trigger_no_timezone #立即发送给所有用户