import json
import os
import sys
import asyncio
import concurrent.futures
import math
import threading
import uuid
from datetime import datetime, time, timezone
from typing import List, Tuple

import firebase_admin
import pytz
from firebase_admin import messaging, firestore
from google.cloud.firestore_v1.base_query import FieldFilter

from modal import Image, App, Secret, Cron
from pydantic import BaseModel, Field
from typing import Optional

# Define the app
app = App(
    name='omi-notifications-standalone',
    secrets=[Secret.from_name("gcp-credentials"), Secret.from_name('envs')],
)

# Create the image with necessary dependencies
image = (
    Image.debian_slim()
    .apt_install('ffmpeg', 'git', 'unzip')
    .pip_install("pytz", "firebase-admin", "google-cloud-firestore", "pydantic", "langchain-core", "langchain-openai", "langchain-community", "tiktoken", "redis", "pinecone-client")
    .env({"PYTHONPATH": "/root"})
    .add_local_dir("utils", remote_path="/root/utils")
    .add_local_dir("models", remote_path="/root/models")
    .add_local_dir("database", remote_path="/root/database")
)

# Notification content constants
DAILY_SUMMARY_TITLE = "Daily summary for today"
MORNING_ALERT_TITLE = "Memorion"
MORNING_ALERT_BODY = "Wear your Memorion device to capture your conversations today."


# Model classes needed for daily summary
class NotificationMessage(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: str = Field(default_factory=lambda: datetime.now(tz=timezone.utc).isoformat())
    sender: str = Field(default='ai')
    plugin_id: Optional[str] = None
    from_integration: str
    type: str
    notification_type: str
    text: Optional[str] = ""
    navigate_to: Optional[str] = None

    @staticmethod
    def get_message_as_dict(message: 'NotificationMessage') -> dict:
        message_dict = message.dict()

        # Remove 'plugin_id' if it is None
        if message.plugin_id is None:
            del message_dict['plugin_id']

        if message.navigate_to is None:
            del message_dict['navigate_to']

        return message_dict


class MessageSender:
    ai = 'ai'
    human = 'human'


class MessageType:
    text = 'text'
    day_summary = 'day_summary'


class Message(BaseModel):
    id: str
    text: str
    created_at: datetime
    sender: str
    app_id: Optional[str] = None
    plugin_id: Optional[str] = None
    from_external_integration: bool = False
    type: str
    memories_id: List[str] = []
    reported: bool = False
    report_reason: Optional[str] = None
    files_id: List[str] = []
    chat_session_id: Optional[str] = None
    data_protection_level: Optional[str] = None


def add_summary_message(text: str, uid: str) -> Message:
    """Add a daily summary message to the user's chat in their active session"""
    db = firestore.client()

    # First, find the session the user is actually using (where they see messages)
    print(f"🔍 Finding active chat session for user {uid}...")

    # Check for session with app_id=None (where the user's messages are)
    user_ref = db.collection('users').document(uid)
    sessions_ref = user_ref.collection('chat_sessions')

    # Look for session with plugin_id=None or app_id=None
    active_session = None
    sessions_query = sessions_ref.where('plugin_id', '==', None).limit(1)
    sessions_docs = list(sessions_query.stream())

    if sessions_docs:
        active_session = sessions_docs[0].to_dict()
        app_id_to_use = None
        session_to_use = active_session
        print(f"✅ Found active session with app_id=None: {active_session['id']}")
    else:
        # Fallback to 'omi' session
        print("🔍 No session with app_id=None found, using 'omi' session...")
        app_id_to_use = 'omi'

        # Look for omi session
        omi_sessions_query = sessions_ref.where('plugin_id', '==', 'omi').limit(1)
        omi_sessions_docs = list(omi_sessions_query.stream())

        if omi_sessions_docs:
            session_to_use = omi_sessions_docs[0].to_dict()
            print(f"✅ Found existing 'omi' session: {session_to_use['id']}")
        else:
            print("📝 Creating new 'omi' session...")
            session_data = {
                'id': str(uuid.uuid4()),
                'created_at': datetime.now(timezone.utc),
                'plugin_id': 'omi',
                'app_id': 'omi',
                'message_ids': [],
                'file_ids': [],
                'title': 'Chat with Omi'
            }
            sessions_ref.document(session_data['id']).set(session_data)
            session_to_use = session_data
            print(f"✅ Created new 'omi' session: {session_to_use['id']}")

    # Create the message with proper session association
    message_id = str(uuid.uuid4())
    ai_message = Message(
        id=message_id,
        text=text,
        created_at=datetime.now(timezone.utc),
        sender='ai',
        app_id=app_id_to_use,
        chat_session_id=session_to_use['id'],  # Associate with the active session
        from_external_integration=False,
        type='day_summary',
        memories_id=[],
    )

    # Add message to Firebase
    message_data = ai_message.dict()
    del message_data['memories_id']  # Remove memories field for storage
    user_ref.collection('messages').add(message_data)

    # Add message to chat session
    session_ref = user_ref.collection('chat_sessions').document(session_to_use['id'])
    session_ref.update({"message_ids": firestore.ArrayUnion([message_id])})

    print(f'[DEBUG] Successfully added daily summary message to Firebase for user {uid}')
    print(f"✅ Daily summary stored with app_id='{app_id_to_use}' and chat_session_id='{session_to_use['id']}'")
    return ai_message


def filter_conversations_by_date(uid: str, start_date: datetime, end_date: datetime):
    """Filter conversations by date range"""
    db = firestore.client()
    user_ref = db.collection('users').document(uid)
    query = (
        user_ref.collection('conversations')
        .where(filter=FieldFilter('created_at', '>=', start_date))
        .where(filter=FieldFilter('created_at', '<=', end_date))
        .where(filter=FieldFilter('discarded', '==', False))
        .order_by('created_at', direction=firestore.Query.DESCENDING)
    )
    conversations = [doc.to_dict() for doc in query.stream()]
    return conversations


def get_conversation_summary(uid: str, memories: List[dict]) -> str:
    """Generate detailed daily summary using the proper LLM integration"""
    if not memories:
        return "No conversations found for today."

    try:
        # Temporarily patch the database client to use Firebase Admin SDK
        import firebase_admin
        from firebase_admin import firestore as admin_firestore

        # Get the existing Firebase app
        app = firebase_admin.get_app()
        admin_db = admin_firestore.client(app)

        # Monkey patch the database module to use the admin client
        import sys
        import types

        # Create a mock database module that uses Firebase Admin
        mock_db_module = types.ModuleType('database._client')
        mock_db_module.db = admin_db

        def document_id_from_seed(seed: str):
            import hashlib
            import uuid
            seed_hash = hashlib.sha256(seed.encode('utf-8')).digest()
            generated_uuid = uuid.UUID(bytes=seed_hash[:16], version=4)
            return str(generated_uuid)

        mock_db_module.document_id_from_seed = document_id_from_seed

        # Temporarily replace the database._client module
        original_client = sys.modules.get('database._client')
        sys.modules['database._client'] = mock_db_module

        try:
            # Import the proper conversation summary function from utils
            from utils.llm.external_integrations import get_conversation_summary as get_detailed_conversation_summary

            # Convert dictionary data to Conversation objects to use the proper summary function
            from models.conversation import Conversation
            import zlib
            import json

            conversation_objects = []
            for memory_dict in memories:
                try:
                    # Handle compressed transcript_segments if present
                    if 'transcript_segments' in memory_dict and isinstance(memory_dict['transcript_segments'], bytes):
                        try:
                            # Decompress the transcript_segments
                            decompressed_data = zlib.decompress(memory_dict['transcript_segments'])
                            memory_dict['transcript_segments'] = json.loads(decompressed_data.decode('utf-8'))
                        except Exception as decomp_error:
                            print(f"Decompression failed: {decomp_error}")
                            memory_dict['transcript_segments'] = []

                    memory = Conversation(**memory_dict)
                    conversation_objects.append(memory)
                except Exception as e:
                    print(f"Error converting memory to Conversation object: {e}")
                    continue

            if not conversation_objects:
                return "No valid conversations found for today."

            # Use the proper detailed summary function from external_integrations
            return get_detailed_conversation_summary(uid, conversation_objects)
        finally:
            # Restore the original module
            if original_client:
                sys.modules['database._client'] = original_client
            elif 'database._client' in sys.modules:
                del sys.modules['database._client']

    except Exception as e:
        print(f"Error in get_conversation_summary: {e}")
        import traceback
        traceback.print_exc()

        # Provide a fallback summary
        conversation_count = len(memories)
        fallback_summary = f"""Conversation Overview
You had {conversation_count} conversations today.
Click to see the summary.

**Technical Note**
Detailed AI-powered summary generation is currently unavailable due to technical issues. This is a basic summary based on conversation metadata.

**Next Steps**
• Review your conversations in the app for detailed insights
• Check back later for AI-generated summaries once the technical issue is resolved
"""
        return fallback_summary


def should_run_job():
    """Check if we should run the job based on current time in different timezones"""
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}  # 8 AM and 10 PM
    print(f'should_run_job: Current UTC time: {current_utc}')

    matching_timezones = []
    for tz in pytz.all_timezones:
        try:
            local_time = current_utc.astimezone(pytz.timezone(tz))
            if local_time.hour in target_hours and local_time.minute == 0:
                matching_timezones.append(f"{tz} ({local_time.strftime('%H:%M')})")
        except Exception:
            # Skip invalid timezones
            continue

    if matching_timezones:
        print(f'should_run_job: Found {len(matching_timezones)} timezones at target hours: {matching_timezones[:5]}...')
        return True

    print('should_run_job: No timezones found at target hours (8 AM or 10 PM)')
    return False


def send_notification(token: str, title: str, body: str, data: dict = None):
    """Enhanced notification sending with improved error handling and logging"""
    print(f'📱 Enhanced send_notification to token: {token[:20]}...')

    # Truncate body for notification display (proven pattern from test scripts)
    notification_body = body[:100] + "..." if len(body) > 100 else body

    # Create basic notification
    notification = messaging.Notification(title=title, body=notification_body)

    # Create message with standard configuration
    message = messaging.Message(
        notification=notification,
        token=token
    )

    if data:
        message.data = data

    try:
        response = messaging.send(message)
        print(f'✅ Enhanced notification sent successfully!')
        print(f'📨 FCM Response: {response}')
        print(f'🎯 Notification details:')
        print(f'   - Title: {title}')
        print(f'   - Body: {notification_body}')
        return True
    except Exception as e:
        error_message = str(e)
        if "Requested entity was not found" in error_message:
            print(f'❌ FCM Token not found or invalid: {token[:20]}...')
            print("💡 User may need to reinstall the app or re-login")
        elif "not a valid FCM registration token" in error_message:
            print(f'❌ Invalid FCM token format: {token[:20]}...')
        else:
            print(f'❌ Enhanced notification failed: {e}')

        print("🔍 Troubleshooting tips:")
        print("   1. Check if user has notifications enabled")
        print("   2. Verify FCM token is still valid")
        print("   3. Ensure app is installed and logged in")
        return False


async def send_bulk_notification(user_tokens: list, title: str, body: str):
    """Enhanced bulk notification sending with improved error handling and logging"""
    try:
        print(f"📤 Sending enhanced bulk notifications to {len(user_tokens)} users")

        batch_size = 500
        num_batches = math.ceil(len(user_tokens) / batch_size)

        # Truncate body for notification display (proven pattern from test scripts)
        notification_body = body[:100] + "..." if len(body) > 100 else body

        def send_batch(batch_users):
            """Send a batch of notifications with standard configuration"""
            messages = [
                messaging.Message(
                    notification=messaging.Notification(title=title, body=notification_body),
                    token=token
                )
                for token in batch_users
            ]

            try:
                response = messaging.send_each(messages)

                # Enhanced logging for delivery verification
                success_count = response.success_count
                failure_count = response.failure_count

                print(f"📊 Batch delivery results:")
                print(f"   ✅ Successful: {success_count}")
                print(f"   ❌ Failed: {failure_count}")

                return response

            except Exception as e:
                print(f"❌ Batch send failed: {e}")
                return None

        tasks = []
        for i in range(num_batches):
            start = i * batch_size
            end = start + batch_size
            batch_users = user_tokens[start:end]
            print(f"📦 Processing batch {i+1}/{num_batches} ({len(batch_users)} users)")
            task = asyncio.to_thread(send_batch, batch_users)
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # Calculate overall statistics
        total_success = sum(r.success_count for r in results if r)
        total_failure = sum(r.failure_count for r in results if r)

        print(f"🎯 Enhanced bulk notification summary:")
        print(f"   📊 Total users targeted: {len(user_tokens)}")
        print(f"   ✅ Successfully delivered: {total_success}")
        print(f"   ❌ Failed deliveries: {total_failure}")
        print(f"   📈 Success rate: {(total_success / len(user_tokens) * 100):.1f}%")

    except Exception as e:
        print(f"❌ Error in enhanced bulk notification: {e}")
        import traceback
        traceback.print_exc()


def get_timezones_at_time(target_time: str):
    """Get all timezones currently at the target time"""
    target_timezones = []
    for tz_name in pytz.all_timezones:
        try:
            tz = pytz.timezone(tz_name)
            current_time = datetime.now(tz).strftime("%H:%M")
            if current_time == target_time:
                target_timezones.append(tz_name)
        except Exception:
            continue
    return target_timezones


async def get_users_token_in_timezones(timezones: List[str]):
    """Get FCM tokens for users in specific timezones"""
    if not timezones:
        return []
    
    db = firestore.client()
    users_ref = db.collection('users')
    user_tokens = []
    
    # Split timezones into chunks of 30 (Firestore 'in' query limit)
    timezone_chunks = [timezones[i : i + 30] for i in range(0, len(timezones), 30)]
    
    async def query_chunk(chunk):
        def sync_query():
            chunk_tokens = []
            try:
                query = users_ref.where(filter=FieldFilter('time_zone', 'in', chunk))
                for doc in query.stream():
                    doc_data = doc.to_dict()
                    if 'fcm_token' in doc_data and doc_data['fcm_token']:
                        chunk_tokens.append(doc_data['fcm_token'])
            except Exception as e:
                print(f"Error querying chunk {chunk}: {e}")
            return chunk_tokens
        
        return await asyncio.to_thread(sync_query)
    
    tasks = [query_chunk(chunk) for chunk in timezone_chunks]
    results = await asyncio.gather(*tasks)
    
    for chunk_tokens in results:
        user_tokens.extend(chunk_tokens)
    
    return user_tokens


async def get_users_id_in_timezones(timezones: List[str]):
    """Get user IDs and FCM tokens for users in specific timezones"""
    if not timezones:
        return []

    db = firestore.client()
    users_ref = db.collection('users')
    user_data = []

    # Split timezones into chunks of 30 (Firestore 'in' query limit)
    timezone_chunks = [timezones[i : i + 30] for i in range(0, len(timezones), 30)]

    async def query_chunk(chunk):
        def sync_query():
            chunk_data = []
            try:
                query = users_ref.where(filter=FieldFilter('time_zone', 'in', chunk))
                for doc in query.stream():
                    doc_data = doc.to_dict()
                    if 'fcm_token' in doc_data and doc_data['fcm_token']:
                        chunk_data.append((doc.id, doc_data['fcm_token']))
            except Exception as e:
                print(f"Error querying chunk {chunk}: {e}")
            return chunk_data

        return await asyncio.to_thread(sync_query)

    tasks = [query_chunk(chunk) for chunk in timezone_chunks]
    results = await asyncio.gather(*tasks)

    for chunk_data in results:
        user_data.extend(chunk_data)

    return user_data


async def send_daily_notification():
    """Send morning notification at 8 AM"""
    try:
        print("Starting send_daily_notification...")
        morning_target_time = "08:00"
        
        timezones_in_time = get_timezones_at_time(morning_target_time)
        user_tokens = await get_users_token_in_timezones(timezones_in_time)
        
        if not user_tokens:
            print("No users found for morning notification")
            return None

        await send_bulk_notification(user_tokens, MORNING_ALERT_TITLE, MORNING_ALERT_BODY)
        print(f"send_daily_notification completed. Users notified: {len(user_tokens)}")
        return user_tokens
        
    except Exception as e:
        print(f"Error in send_daily_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


def extract_notification_content(summary: str) -> tuple[str, str]:
    """Extract title and body from the generated summary for FCM notification"""
    print(f"\n📝 EXTRACTING NOTIFICATION CONTENT:")
    print(f"   - Original summary length: {len(summary)} characters")

    # Look for the first markdown header (e.g., "**每日亮点**")
    lines = summary.split('\n')
    extracted_title = DAILY_SUMMARY_TITLE  # Default fallback
    extracted_body = summary  # Default fallback

    for i, line in enumerate(lines):
        line = line.strip()
        # Check if line is a markdown bold header (starts and ends with **)
        if line.startswith('**') and line.endswith('**') and len(line) > 4:
            # Extract the title (remove the ** markers)
            extracted_title = line[2:-2].strip()
            print(f"   - Found header: '{extracted_title}'")

            # Extract the body (everything after this header)
            remaining_lines = lines[i+1:]
            # Skip empty lines after the header
            while remaining_lines and not remaining_lines[0].strip():
                remaining_lines = remaining_lines[1:]

            extracted_body = '\n'.join(remaining_lines).strip()
            print(f"   - Extracted body length: {len(extracted_body)} characters")
            print(f"   - Body preview: {extracted_body[:100]}...")
            break

    if extracted_title == DAILY_SUMMARY_TITLE:
        print(f"   - No header found, using default title: '{DAILY_SUMMARY_TITLE}'")
        print(f"   - Using full summary as body")

    return extracted_title, extracted_body


def _send_summary_notification(user_data: tuple):
    """Send individual daily summary notification with content and navigation"""
    uid = user_data[0]
    fcm_token = user_data[1]

    print(f"📱 Processing enhanced summary notification for user {uid[:8]}...")

    try:
        # Get today's conversations for the user
        memories_data = filter_conversations_by_date(
            uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
        )

        if not memories_data:
            print(f"No conversations found for user {uid}, skipping notification")
            return

        # Generate summary content
        summary = get_conversation_summary(uid, memories_data)

        # 📊 DETAILED SUMMARY CONTENT LOGGING
        print("=" * 80)
        print("📊 GENERATED SUMMARY CONTENT VERIFICATION")
        print("=" * 80)
        print(f"👤 User ID: {uid[:8]}...")
        print(f"📅 Timestamp: {datetime.now(timezone.utc).isoformat()}")
        print(f"📏 Summary Length: {len(summary)} characters")
        print(f"📝 Conversation Count: {len(memories_data)} conversations processed")

        # Content preview - first 200 characters
        print(f"\n📖 SUMMARY CONTENT PREVIEW (First 200 chars):")
        print("-" * 50)
        print(f"{summary[:200]}{'...' if len(summary) > 200 else ''}")

        # Content preview - last 200 characters
        if len(summary) > 400:
            print(f"\n📖 SUMMARY CONTENT PREVIEW (Last 200 chars):")
            print("-" * 50)
            print(f"...{summary[-200:]}")

        # Full content logging
        print(f"\n📄 COMPLETE SUMMARY CONTENT:")
        print("=" * 50)
        print(summary)
        print("=" * 50)

        # Summary structure analysis
        lines = summary.split('\n')
        print(f"\n🔍 SUMMARY STRUCTURE ANALYSIS:")
        print(f"   📋 Total lines: {len(lines)}")

        # Look for markdown headers
        headers_found = []
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if line_stripped.startswith('**') and line_stripped.endswith('**') and len(line_stripped) > 4:
                header = line_stripped[2:-2].strip()
                headers_found.append(f"Line {i+1}: '{header}'")

        if headers_found:
            print(f"   🏷️  Markdown headers found: {len(headers_found)}")
            for header in headers_found:
                print(f"      • {header}")
        else:
            print(f"   ⚠️  No markdown headers found")

        # Content quality indicators
        print(f"\n✅ CONTENT QUALITY INDICATORS:")
        chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in summary)
        english_chars = any(char.isalpha() and ord(char) < 128 for char in summary)
        has_numbers = any(char.isdigit() for char in summary)
        quote_chars = ['"', '"', '"', "'", '"']
        has_quotes = any(q in summary for q in quote_chars)
        bullet_indicators = ['•', '·']
        line_starters = ('1.', '2.', '3.', '-', '*')
        has_bullets = any(b in summary for b in bullet_indicators) or any(line.strip().startswith(line_starters) for line in lines)

        print(f"   📊 Contains Chinese characters: {'Yes' if chinese_chars else 'No'}")
        print(f"   📊 Contains English characters: {'Yes' if english_chars else 'No'}")
        print(f"   📊 Contains numbers: {'Yes' if has_numbers else 'No'}")
        print(f"   📊 Contains quotes: {'Yes' if has_quotes else 'No'}")
        print(f"   📊 Contains bullet points: {'Yes' if has_bullets else 'No'}")

        print("=" * 80)
        print("✅ SUMMARY CONTENT VERIFICATION COMPLETE")
        print("=" * 80)

        # Store the FULL summary in the user's chat (database gets complete content)
        add_summary_message(summary, uid)

        # Extract title and body for FCM notification
        notification_title, notification_body = extract_notification_content(summary)

        # Create notification message with navigation data (using extracted body)
        ai_message = NotificationMessage(
            text=notification_body,  # Use extracted body for notification
            from_integration='false',
            type='day_summary',
            notification_type='daily_summary',
            navigate_to="/chat/omi",  # This enables navigation to daily summary
        )

        # Send notification with extracted title and body
        send_notification(fcm_token, notification_title, notification_body, NotificationMessage.get_message_as_dict(ai_message))
        print(f"Daily summary notification sent to user {uid}")

    except Exception as e:
        print(f"Error sending summary notification to user {uid}: {e}")


async def _send_bulk_summary_notification(users: list):
    """Send daily summary notifications to multiple users"""
    loop = asyncio.get_running_loop()
    with concurrent.futures.ThreadPoolExecutor() as pool:
        tasks = [loop.run_in_executor(pool, _send_summary_notification, user_data) for user_data in users]
        await asyncio.gather(*tasks)


async def send_daily_summary_notification():
    """Send evening summary notification at 10 PM with proper navigation"""
    try:
        print("Starting send_daily_summary_notification...")
        daily_summary_target_time = "22:00"

        timezones_in_time = get_timezones_at_time(daily_summary_target_time)
        user_data = await get_users_id_in_timezones(timezones_in_time)

        if not user_data:
            print("No users found for daily summary notification")
            return None

        print(f"Found {len(user_data)} users for daily summary notification")
        await _send_bulk_summary_notification(user_data)
        print(f"send_daily_summary_notification completed. Users notified: {len(user_data)}")
        return user_data

    except Exception as e:
        print(f"Error in send_daily_summary_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


async def start_cron_job():
    """Main cron job function"""
    print(f'start_cron_job called at {datetime.now(pytz.utc)}')
    if should_run_job():
        print('start_cron_job: should_run_job returned True, sending notifications...')
        try:
            await send_daily_notification()
            await send_daily_summary_notification()
            print('start_cron_job: All notifications sent successfully')
        except Exception as e:
            print(f'Error in start_cron_job: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('start_cron_job: should_run_job returned False, no notifications to send')


@app.function(
    image=image,
    schedule=Cron('* * * * *'),
    timeout=300,
    memory=512,
)
async def notifications_cronjob():
    """Scheduled notification cron job"""
    print(f'Notification cron job started at {datetime.now(pytz.utc)}')
    
    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}
    
    try:
        await start_cron_job()
        return {"status": "success", "message": "Cron job completed", "ran_at": str(datetime.now(pytz.utc))}
    except Exception as e:
        print(f'Error in notifications_cronjob: {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}


@app.function(
    image=image,
    timeout=300,
    memory=512,
)
async def manual_notification_trigger():
    """Manual trigger for testing notifications"""
    print(f'Manual notification trigger started at {datetime.now(pytz.utc)}')

    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}

    try:
        await start_cron_job()
        print('Manual notification trigger completed successfully')
        return {"status": "success", "message": "Manual notifications completed", "ran_at": str(datetime.now(pytz.utc))}
    except Exception as e:
        print(f'Error in manual notification trigger: {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}


@app.function(
    image=image,
    timeout=300,
    memory=512,
)
async def manual_notification_trigger_no_timezone():
    """Manual trigger for testing notifications without timezone restrictions"""
    print(f'Manual notification trigger (no timezone check) started at {datetime.now(pytz.utc)}')

    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}

    try:
        print('Bypassing timezone check - sending notifications to all users...')

        # Get all users with FCM tokens (no timezone filtering)
        db = firestore.client()
        users_ref = db.collection('users')

        # Get all users who have FCM tokens
        all_users_with_tokens = []
        all_users_data = []  # For summary notifications

        print('Fetching all users with FCM tokens...')
        for doc in users_ref.stream():
            doc_data = doc.to_dict()
            if 'fcm_token' in doc_data and doc_data['fcm_token']:
                all_users_with_tokens.append(doc_data['fcm_token'])
                all_users_data.append((doc.id, doc_data['fcm_token']))

        print(f'Found {len(all_users_with_tokens)} users with FCM tokens')

        # Send morning notifications to all users
        if all_users_with_tokens:
            print('Sending morning notifications...')
            await send_bulk_notification(all_users_with_tokens, MORNING_ALERT_TITLE, MORNING_ALERT_BODY)

        # Send summary notifications to all users
        if all_users_data:
            print('Sending summary notifications...')
            await _send_bulk_summary_notification(all_users_data)

        print('Manual notification trigger (no timezone) completed successfully')
        return {
            "status": "success",
            "message": f"Manual notifications sent to {len(all_users_with_tokens)} users (no timezone check)",
            "users_notified": len(all_users_with_tokens),
            "ran_at": str(datetime.now(pytz.utc))
        }
    except Exception as e:
        print(f'Error in manual notification trigger (no timezone): {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}
